package com.ymx.photovoltaic.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.bean.Collector
import com.ymx.photovoltaic.data.bean.Group
import com.ymx.photovoltaic.data.bean.Optimizer
import com.ymx.photovoltaic.data.bean.Relay
import com.ymx.photovoltaic.ui.page.common.ScanViewPage
import com.ymx.photovoltaic.ui.page.common.WebViewPage
import com.ymx.photovoltaic.ui.page.equipment.CollectorConfigScreen
import com.ymx.photovoltaic.ui.page.equipment.CollectorOnePage
import com.ymx.photovoltaic.ui.page.equipment.DeviceConfigScreen
import com.ymx.photovoltaic.ui.page.equipment.EquipmentListPage
import com.ymx.photovoltaic.ui.page.equipment.GroupConfigScreen
import com.ymx.photovoltaic.ui.page.equipment.GroupListPage
import com.ymx.photovoltaic.ui.page.equipment.GroupOnePage
import com.ymx.photovoltaic.ui.page.equipment.OptimizerConfigScreen
import com.ymx.photovoltaic.ui.page.equipment.OptimizerForRelayPage
import com.ymx.photovoltaic.ui.page.equipment.OptimizerListPage
import com.ymx.photovoltaic.ui.page.equipment.OptimizerOnePage
import com.ymx.photovoltaic.ui.page.equipment.RelayConfigScreen
import com.ymx.photovoltaic.ui.page.equipment.RelayOnePage
import com.ymx.photovoltaic.ui.page.equipment.RelayOptimizerSelectPage
import com.ymx.photovoltaic.ui.page.equipment.SetCollectorPage
import com.ymx.photovoltaic.ui.page.equipment.SetRelayPage
import com.ymx.photovoltaic.ui.page.equipment.StationOnePage
import com.ymx.photovoltaic.ui.page.home.HomePage
import com.ymx.photovoltaic.ui.page.home.edit.EditPage
import com.ymx.photovoltaic.ui.page.home.edit.KeepWarnPage
import com.ymx.photovoltaic.ui.page.home.edit.OwnerOnePage
import com.ymx.photovoltaic.ui.page.home.edit.StationInfoPage
import com.ymx.photovoltaic.ui.page.home.edit.TempWarnPage
import com.ymx.photovoltaic.ui.page.home.report.ReportPage
import com.ymx.photovoltaic.ui.page.home.station.StationViewPage
import com.ymx.photovoltaic.ui.page.message.MessageDetailPage
import com.ymx.photovoltaic.ui.page.message.MessagePage
import com.ymx.photovoltaic.ui.page.my.ChangePwdPage
import com.ymx.photovoltaic.ui.page.my.FeedbackPage
import com.ymx.photovoltaic.ui.page.my.MyPage
import com.ymx.photovoltaic.ui.page.my.SamplingTestPage
import com.ymx.photovoltaic.ui.page.my.SettingPage
import com.ymx.photovoltaic.ui.page.user.AccountTypePage
import com.ymx.photovoltaic.ui.page.user.ForgetPasswordPage
import com.ymx.photovoltaic.ui.page.user.LoginPage
import com.ymx.photovoltaic.ui.page.user.RegisterPage
import com.ymx.photovoltaic.ui.page.user.SplashScreen
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.Json

@Composable
fun StartNavGraph(navHostController: NavHostController) {
    NavHost(
        navController = navHostController,
        startDestination = Route.SPLASH,
    ) {
        // Splash & 用户认证相关
        composable(Route.SPLASH) {
            SplashScreen(navHostController)
        }
        
        composable(Route.LOGIN) {
            LoginPage(navHostController)
        }

        composable(Route.ACCOUNT_TYPE) {
            AccountTypePage(navHostController)
        }

        composable(
            route = "${Route.REGISTER}?accountType={accountType}",
            arguments = listOf(
                navArgument("accountType") {
                    type = NavType.StringType
                    nullable = true
                    defaultValue = null
                }
            )
        ) { backStackEntry ->
            val accountType = backStackEntry.arguments?.getString("accountType")
            RegisterPage(navHostController, accountType = accountType)
        }

        composable(Route.FORGOT_PASSWORD) {
            ForgetPasswordPage(navHostController)
        }

        // 主页面
        composable(Route.HOME) {
            HomePage(navHostController)
        }

        composable(Route.MESSAGE) {
            MessagePage(navHostController)
        }
        
        composable(Route.MY) {
            MyPage(navHostController)
        }

        composable(Route.EQUIPMENT) {
            EquipmentListPage(navHostController)
        }

        // 消息详情
        composable(
            Route.MESSAGE_DETAIL,
            arguments = listOf(
                navArgument("id") { type = NavType.StringType },
                navArgument("type") { type = NavType.StringType },
                navArgument("content") { type = NavType.StringType },
                navArgument("time") { type = NavType.StringType },
                navArgument("isRead") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val id = backStackEntry.arguments?.getString("id") ?: ""
            val type = backStackEntry.arguments?.getString("type") ?: "1"
            val content = backStackEntry.arguments?.getString("content") ?: ""
            val time = backStackEntry.arguments?.getString("time") ?: "0"
            val isRead = backStackEntry.arguments?.getString("isRead") ?: "false"
            
            MessageDetailPage(
                navHostController = navHostController,
                id = id,
                type = type,
                content = content,
                time = time,
                isRead = isRead
            )
        }

        // Web视图
        composable(
            route = Route.WEB_VIEW,
            arguments = listOf(
                navArgument("title") { type = NavType.StringType },
                navArgument("url") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val title = backStackEntry.arguments?.getString("title") ?: ""
            val url = backStackEntry.arguments?.getString("url") ?: ""
            WebViewPage(navController = navHostController, title = title, url = url)
        }

        // 个人设置
        composable(Route.SETTING) {
            SettingPage(navHostController = navHostController)
        }
        
        composable(Route.FEEDBACK) {
            FeedbackPage(navHostController = navHostController)
        }
        
        composable(Route.CHANGE_PASSWORD) {
            ChangePwdPage(navHostController = navHostController)
        }

        // 优化器
        composable(
            Route.OPTIMIZER + "/{groupId}", arguments = listOf(
                navArgument("groupId") { type = NavType.StringType },
            )
        ) { backStackEntry ->
            val groupId = backStackEntry.arguments?.getString("groupId") ?: ""
            OptimizerListPage(navHostController = navHostController, groupId = groupId)
        }

        composable(
            Route.OPTIMIZER_NEW + "/{groupId}/{status}",
            arguments = listOf(
                navArgument("groupId") { type = NavType.StringType },
                navArgument("status") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val groupId = backStackEntry.arguments?.getString("groupId") ?: ""
            val status = backStackEntry.arguments?.getString("status") ?: "1"

            OptimizerOnePage(
                navHostController,
                id = "",
                chipId = "",
                model = "",
                status = status,
                groupId = groupId
            )
        }

        composable(
            Route.OPTIMIZER_EDIT + "/{id}/{chipId}/{model}/{status}",
            arguments = listOf(
                navArgument("id") { type = NavType.StringType },
                navArgument("chipId") { type = NavType.StringType },
                navArgument("model") { type = NavType.StringType },
                navArgument("status") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val id = backStackEntry.arguments?.getString("id") ?: ""
            val chipId = backStackEntry.arguments?.getString("chipId") ?: ""
            val model = backStackEntry.arguments?.getString("model") ?: ""
            val status = backStackEntry.arguments?.getString("status") ?: "1"

            OptimizerOnePage(
                navHostController,
                id = id,
                chipId = chipId,
                model = model,
                status = status,
                groupId = ""
            )
        }

        // 组
        composable(
            Route.GROUP + "/{collectorId}", arguments = listOf(
                navArgument("collectorId") { type = NavType.StringType },
            )
        ) { backStackEntry ->
            val collectorId = backStackEntry.arguments?.getString("collectorId") ?: ""
            GroupListPage(navHostController = navHostController, collectorId = collectorId)
        }

        composable(Route.GROUP_NEW+ "/{collectorId}/{imei}", arguments = listOf(
            navArgument("collectorId") { type = NavType.StringType },
            navArgument("imei") { type = NavType.StringType }
        )) { backStackEntry ->
            val collectorId = backStackEntry.arguments?.getString("collectorId") ?: ""
            val imei = backStackEntry.arguments?.getString("imei") ?: ""
            GroupOnePage(navHostController, groupId ="",
                groupName = "", power = "", collectorId =collectorId, imei = imei )
        }

        composable(Route.GROUP_EDIT + "/{groupId}/{groupName}/{power}/{imei}",
            arguments = listOf(
                navArgument("groupId") { type = NavType.StringType },
                navArgument("groupName") { type = NavType.StringType },
                navArgument("power") { type = NavType.StringType },
                navArgument("imei") { type = NavType.StringType }
            )) { backStackEntry ->
            val groupId = backStackEntry.arguments?.getString("groupId") ?: ""
            val groupName = backStackEntry.arguments?.getString("groupName") ?: ""
            val power = backStackEntry.arguments?.getString("power") ?: ""
            val imei = backStackEntry.arguments?.getString("imei") ?: ""

            GroupOnePage(navHostController, groupId=groupId,
                groupName = groupName, power = power,collectorId = "", imei = imei)
        }

        composable(Route.STATION_NEW) {
            StationOnePage(navHostController)
        }

        composable(Route.COLLECTOR_NEW) {
            CollectorOnePage(navHostController, collectorId ="",
                collectorName = "", imei = "")
        }

        composable(Route.COLLECTOR_EDIT + "/{collectorId}/{collectorName}/{imei}",
            arguments = listOf(
                navArgument("collectorName") { type = NavType.StringType },
                navArgument("imei") { type = NavType.StringType },
                navArgument("collectorId") { type = NavType.StringType }
            )) { backStackEntry ->
            val collectorId = backStackEntry.arguments?.getString("collectorId") ?: ""
            val collectorName = backStackEntry.arguments?.getString("collectorName") ?: ""
            val imei = backStackEntry.arguments?.getString("imei") ?: ""
            CollectorOnePage(navHostController, collectorId=collectorId,
                collectorName = collectorName, imei = imei)
        }

        // 二级主导航
        composable(Route.VIEW) {
            StationViewPage(navHostController)
        }

        // 扫描相关
        composable(
            Route.SCAN + "/{groupId}",
            arguments = listOf(
                navArgument("groupId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val groupId = backStackEntry.arguments?.getString("groupId") ?: ""
            ScanViewPage(navHostController, groupId = groupId)
        }


        composable(Route.TEST_SCAN) {
            ScanViewPage(navHostController)
        }

        composable(Route.VERIFY_SCAN) {
            ScanViewPage(navHostController)
        }

        composable(Route.SCAN) {
            ScanViewPage(navHostController)
        }

        // 中继相关
        composable(Route.RELAY_NEW+"/{imei}",
            arguments = listOf(
                navArgument("imei") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val imei = backStackEntry.arguments?.getString("imei") ?: ""
            RelayOnePage(
                navHostController,
                relayId = "",
                relayName = "",
                imei = imei
            )
        }

        composable(Route.RELAY_EDIT + "/{relayId}/{relayName}/{id}",
            arguments = listOf(
                navArgument("relayId") { type = NavType.StringType },
                navArgument("relayName") { type = NavType.StringType },
                navArgument("id") { type = NavType.IntType },
            )) { backStackEntry ->
            val relayId = backStackEntry.arguments?.getString("relayId") ?: ""
            val relayName = backStackEntry.arguments?.getString("relayName") ?: ""
            val id = backStackEntry.arguments?.getInt("id")
            RelayOnePage(
                navHostController,
                relayId = relayId,
                relayName = relayName,
                id = id
            )
        }

        composable(
            Route.OPTIMIZER_FOR_RELAY + "/{relayId}",
            arguments = listOf(
                navArgument("relayId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val relayId = backStackEntry.arguments?.getString("relayId") ?: ""
            OptimizerForRelayPage(
                navHostController = navHostController,
                relayId = relayId
            )
        }

        composable(Route.SAMPLING_TEST) {
            SamplingTestPage(navHostController)
        }

        composable(Route.SET_COLLECTOR) {
            SetCollectorPage(navHostController)
        }

        composable(Route.OWNER_ONE) {
            OwnerOnePage(navHostController)
        }

        composable(Route.SET_RELAY + "/{imei}",
            arguments = listOf(
                navArgument("imei") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val imei = backStackEntry.arguments?.getString("imei") ?: ""
            SetRelayPage(
                navHostController = navHostController,
                imei = imei
            )
        }

        composable(
            Route.RELAY_OPTIMIZER_SELECT + "/{relayId}/{createUserId}/{flag}",
            arguments = listOf(
                navArgument("relayId") { type = NavType.StringType },
                navArgument("createUserId") { type = NavType.StringType },
                navArgument("flag") { type = NavType.IntType }
            )
        ) { backStackEntry ->
            val relayId = backStackEntry.arguments?.getString("relayId") ?: ""
            val createUserId = backStackEntry.arguments?.getString("createUserId") ?: ""
            val flag = backStackEntry.arguments?.getInt("flag") ?: 1
            
            RelayOptimizerSelectPage(
                navHostController = navHostController,
                relayId = relayId,
                createUserId = createUserId,
                flag = flag
            )
        }

        // 配置相关
        composable(
            Route.RELAY_CONFIG
        ) { 
            val relayListJson = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("relayList") ?: "[]"
            
            val relayList = Json.decodeFromString(ListSerializer(Relay.serializer()), relayListJson)
            
            // 获取imei参数
            val imei = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("imei") ?: ""
            
            RelayConfigScreen(
                navHostController = navHostController,
                relayList = relayList,
                imei = imei
            )
        }

        composable(
            Route.COLLECTOR_CONFIG) {
            val collectorListJson = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("collectorList") ?: "[]"
            
            val collectorList = Json.decodeFromString(ListSerializer(Collector.serializer()), collectorListJson)
            
            CollectorConfigScreen(
                navHostController = navHostController,
                collectorList = collectorList
            )
        }
        
        composable(
            Route.GROUP_CONFIG
        ) { 
            val groupListJson = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("groupList") ?: "[]"
            
            val stationName = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("stationName") ?: ""
            
            // 获取collectorId参数
            val collectorId = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("collectorId") ?: ""

            val imei = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("imei") ?: ""
            
            val groupList = Json.decodeFromString(ListSerializer(Group.serializer()), groupListJson)
            
            GroupConfigScreen(
                navHostController = navHostController,
                groupList = groupList,
                stationName = stationName,
                collectorId = collectorId,
                imei=imei
            )
        }

        composable(
            Route.DEVICE_CONFIG + "/{stationId}/{systemName}/{address}/{power}/{createTime}/{daysSinceTime}/{status}/{index}/{powerStationType}",
            arguments = listOf(
                navArgument("stationId") { type = NavType.StringType },
                navArgument("systemName") { type = NavType.StringType },
                navArgument("address") { type = NavType.StringType },
                navArgument("power") { type = NavType.IntType },
                navArgument("createTime") { type = NavType.StringType },
                navArgument("daysSinceTime") { type = NavType.LongType },
                navArgument("status") { type = NavType.StringType },
                navArgument("index") { type = NavType.StringType },
                navArgument("powerStationType") { type = NavType.IntType }
            )
        ) { backStackEntry ->
            val stationId = backStackEntry.arguments?.getString("stationId") ?: ""
            val systemName = backStackEntry.arguments?.getString("systemName") ?: ""
            val address = backStackEntry.arguments?.getString("address") ?: ""
            val power = backStackEntry.arguments?.getInt("power") ?: 0
            val createTime = backStackEntry.arguments?.getString("createTime") ?: ""
            val daysSinceTime = backStackEntry.arguments?.getLong("daysSinceTime") ?: 0L
            val status = backStackEntry.arguments?.getString("status") ?: ""
            val index = backStackEntry.arguments?.getString("index") ?: "0"
            val powerStationType = backStackEntry.arguments?.getInt("powerStationType") ?: 0
            AppGlobal.powerStationId=stationId
            AppGlobal.powerStationType=powerStationType

            DeviceConfigScreen(
                navHostController = navHostController,
                stationId = stationId,
                systemName = systemName,
                address = address,
                power = power,
                createTime = createTime,
                daysSinceTime = daysSinceTime,
                status = status,
                index = index.toInt()
            )
        }

        composable(
            Route.OPTIMIZER_CONFIG
        ) { 
            val optimizerListJson = navHostController.previousBackStackEntry
                ?.savedStateHandle
                ?.get<String>("optimizerList") ?: "[]"
            
            val optimizerList = Json.decodeFromString(ListSerializer(Optimizer.serializer()), optimizerListJson)
            
            OptimizerConfigScreen(
                navHostController = navHostController,
                optimizerList = optimizerList
            )
        }


        composable(
            Route.REPORT + "/{powId}/{sunUpTime}/{sunDownTime}/{createTime}/{districtId}/{stationName}/{status}/{power}",
            arguments = listOf(
                navArgument("powId") { type = NavType.StringType },
                navArgument("sunUpTime") { type = NavType.StringType },
                navArgument("sunDownTime") { type = NavType.StringType },
                navArgument("createTime") { type = NavType.LongType },
                navArgument("districtId") { type = NavType.StringType },
                navArgument("stationName") { type = NavType.StringType },
                navArgument("status") { type = NavType.StringType },
                navArgument("power") { type = NavType.IntType },
            )
        ) { backStackEntry ->
            val powId = backStackEntry.arguments?.getString("powId")
            val sunUpTime = backStackEntry.arguments?.getString("sunUpTime")
            val sunDownTime = backStackEntry.arguments?.getString("sunDownTime")
            val createTime = backStackEntry.arguments?.getLong("createTime")
            val districtId = backStackEntry.arguments?.getString("districtId")
            val stationName = backStackEntry.arguments?.getString("stationName")
            val status = backStackEntry.arguments?.getString("status")
            val power = backStackEntry.arguments?.getInt("power") ?: 0

            AppGlobal.powerStationId = powId!!
            AppGlobal.powerDistrictId = districtId!!
            AppGlobal.sunUpTime = sunUpTime!!
            AppGlobal.sunDownTime = sunDownTime!!
            AppGlobal.powCreateTime = createTime!!
            AppGlobal.powerStationName = stationName!!
            AppGlobal.powerStatus = status!!
            AppGlobal.stationPower = power
            
            ReportPage(navHostController)
        }

        composable(Route.REPORT) {
            ReportPage(navHostController)
        }
        
        composable(Route.EDIT) {
            EditPage(navHostController)
        }
        
        composable(Route.KEEP_WARN) {
            KeepWarnPage(navHostController)
        }
        
        composable(Route.TEMP_WARN) {
            TempWarnPage(navHostController)
        }
        
        composable(Route.STATION_INFO) {
            StationInfoPage(navHostController)
        }
    }
}
