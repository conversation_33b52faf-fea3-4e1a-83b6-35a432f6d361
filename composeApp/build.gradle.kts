import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidApplication)
    alias(libs.plugins.composeMultiplatform)
    alias(libs.plugins.composeCompiler)
    alias(libs.plugins.serialization.plugin)
}

kotlin {

    androidTarget {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_17)
        }
    }
    
    listOf(
        iosX64(),
        iosArm64(),
        iosSimulatorArm64()
    ).forEach { iosTarget ->
        iosTarget.binaries.framework {
            baseName = "ComposeApp"
            isStatic = true
            compilerOptions {
                freeCompilerArgs.addAll(
                    "-opt-in=kotlin.RequiresOptIn",
                    "-opt-in=kotlin.js.ExperimentalJsExport",
                    "-opt-in=kotlin.native.concurrent.SharedImmutable"
                )
            }
        }
    }

    sourceSets {

        iosMain.dependencies {
            implementation("io.ktor:ktor-client-darwin:${libs.versions.ktor.get()}")

        }
        
        androidMain.dependencies {
            implementation(compose.preview)
            implementation(libs.androidx.activity.compose)

            implementation(libs.koin.android)
            implementation(libs.koin.androidx.compose)
            implementation(libs.ktor.client.android)
        }
        
        commonMain.dependencies {

            implementation(compose.runtime)
            implementation(compose.foundation)
            implementation(compose.material)
            implementation(compose.material3)
            implementation(compose.ui)
            implementation(compose.components.resources)
            implementation(compose.components.uiToolingPreview)
            implementation(libs.androidx.lifecycle.viewmodel)
            implementation(libs.androidx.lifecycle.runtime.compose)
            implementation(libs.serialization)
            implementation(libs.jet.navigation.compose)

            implementation(libs.koin.core)

            implementation(libs.ktor.client.core)

            implementation(libs.ktor.client.content.negotiation)
            implementation(libs.ktor.serialization.kotlinx.json)
            implementation(libs.ktor.client.logging)
            implementation(libs.ktor.network)


            implementation(libs.kotlinx.datetime)
            implementation(libs.multiplatform.settings)
            implementation(libs.multiplatform.settings.no.arg)

            implementation(libs.vico.compose)
            implementation(libs.vico.compose.m2)
            implementation(libs.vico.compose.m3)

            api(libs.compose.webview.multiplatform)
            implementation(libs.scanner)



        }
    }
}

android {
    namespace = "com.ymx.photovoltaic"
    compileSdk = libs.versions.android.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "com.ymx.photovoltaic"
        minSdk = libs.versions.android.minSdk.get().toInt()
        targetSdk = libs.versions.android.targetSdk.get().toInt()
        versionCode = 1
        versionName = "*******"

        manifestPlaceholders["JPUSH_PKGNAME"]="com.ymx.photovoltaic"
        manifestPlaceholders["JPUSH_APPKEY"]="639885d9b5a699792570f4e4"
        manifestPlaceholders["JPUSH_CHANNEL"]="developer-default"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
    buildTypes {
        getByName("release") {
            isMinifyEnabled = true
            isShrinkResources= true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    buildFeatures {
        compose = true
        buildConfig = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    flavorDimensions += "brand"
    productFlavors {
        create("ymx") {
            dimension = "brand"
            manifestPlaceholders += mapOf(
                "app_name" to "@string/app_name_ymx",
                "app_icon" to "@mipmap/ic_launcher",
                "app_icon_round" to "@mipmap/ic_launcher_round",
                "app_theme" to "@style/Theme.App.Starting.Ymx"
            )
        }

        create("lj") {
            dimension = "brand"
            manifestPlaceholders += mapOf(
                "app_name" to "@string/app_name_lj",
                "app_icon" to "@mipmap/lj_launcher_new",
                "app_icon_round" to "@mipmap/lj_launcher_new_round",
                "app_theme" to "@style/Theme.App.Starting.Lj"
            )
        }
    }

    applicationVariants.all {
        val versionName = this.versionName
        val flavorName = this.flavorName
        val buildType = this.buildType.name

        outputs.all {
            (this as? com.android.build.gradle.internal.api.BaseVariantOutputImpl)?.outputFileName =
                "${flavorName}-photovoltaic-app-${buildType}-${versionName}.apk"
        }
    }
}

dependencies {
    debugImplementation(compose.uiTooling)
}

